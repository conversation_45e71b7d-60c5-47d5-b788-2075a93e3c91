<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessMedia extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'media_url',
        'media_type',
        'caption',
    ];

    /**
     * Get the business that owns the media.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }
}
