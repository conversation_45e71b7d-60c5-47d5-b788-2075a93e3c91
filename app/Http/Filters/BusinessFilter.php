<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BusinessFilter
{
    protected Request $request;
    protected Builder $builder;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function apply(Builder $builder): Builder
    {
        $this->builder = $builder;

        foreach ($this->filters() as $name => $value) {
            if (method_exists($this, $name) && $value !== null) {
                $this->$name($value);
            }
        }

        return $this->builder;
    }

    protected function filters(): array
    {
        return $this->request->only([
            'search',
            'category',
            'rating',
            'lat',
            'lng'
        ]);
    }

    protected function search(string $value): void
    {
        $this->builder->where(function ($query) use ($value) {
            $query->where('name', 'like', "%{$value}%")
                  ->orWhere('description', 'like', "%{$value}%");
        });
    }

    protected function category(string $value): void
    {
        $this->builder->whereHas('categories', function ($query) use ($value) {
            $query->where('slug', $value);
        });
    }

    protected function rating(int $value): void
    {
        $this->builder->where('average_rating', '>=', $value);
    }

    protected function lat(float $lat): void
    {
        $lng = $this->request->get('lng');
        
        if ($lng !== null) {
            $this->addDistanceCalculation($lat, $lng);
        }
    }

    protected function lng(float $lng): void
    {
        // This is handled in the lat method
    }

    protected function addDistanceCalculation(float $lat, float $lng): void
    {
        $this->builder->select('businesses.*')
            ->selectRaw(
                '(6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance_km',
                [$lat, $lng, $lat]
            )
            ->orderBy('distance_km');
    }
}
