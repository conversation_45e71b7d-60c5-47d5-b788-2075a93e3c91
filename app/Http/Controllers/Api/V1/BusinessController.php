<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Filters\BusinessFilter;
use App\Http\Resources\BusinessDetailResource;
use App\Http\Resources\BusinessSummaryResource;
use App\Models\Business;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BusinessController extends Controller
{
    /**
     * Get paginated list of businesses with filtering.
     */
    public function index(Request $request): JsonResponse
    {
        $filter = new BusinessFilter($request);

        $businesses = Business::filter($filter)
            ->where('is_active', true)
            ->paginate(15);

        // Add distance_km to each business if lat/lng provided
        if ($request->has(['lat', 'lng'])) {
            $businesses->getCollection()->transform(function ($business) {
                if (isset($business->distance_km)) {
                    $business->distance_km = round($business->distance_km, 2);
                }
                return $business;
            });
        }

        return response()->json([
            'data' => BusinessSummaryResource::collection($businesses),
            'meta' => [
                'current_page' => $businesses->currentPage(),
                'last_page' => $businesses->lastPage(),
                'per_page' => $businesses->perPage(),
                'total' => $businesses->total(),
            ]
        ]);
    }

    /**
     * Get a single business by slug.
     */
    public function show(Business $business): JsonResponse
    {
        $business->load([
            'categories',
            'hours',
            'reviews.user',
            'media'
        ]);

        return response()->json([
            'data' => new BusinessDetailResource($business)
        ]);
    }
}
