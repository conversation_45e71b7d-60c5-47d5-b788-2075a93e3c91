<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessSummaryResource;
use App\Models\Business;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FavoriteController extends Controller
{
    /**
     * Toggle favorite status for a business.
     */
    public function toggle(Request $request, Business $business): JsonResponse
    {
        $user = $request->user();
        $user->favorites()->toggle($business->id);

        $isFavorited = $user->favorites()->where('business_id', $business->id)->exists();

        return response()->json([
            'data' => [
                'is_favorited' => $isFavorited,
                'message' => $isFavorited ? 'Business added to favorites' : 'Business removed from favorites'
            ]
        ]);
    }

    /**
     * Get user's favorited businesses.
     */
    public function index(Request $request): JsonResponse
    {
        $favorites = $request->user()
            ->favorites()
            ->where('is_active', true)
            ->paginate(15);

        return response()->json([
            'data' => BusinessSummaryResource::collection($favorites),
            'meta' => [
                'current_page' => $favorites->currentPage(),
                'last_page' => $favorites->lastPage(),
                'per_page' => $favorites->perPage(),
                'total' => $favorites->total(),
            ]
        ]);
    }
}
