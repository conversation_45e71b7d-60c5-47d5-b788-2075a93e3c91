<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessMediaResource;
use App\Models\BusinessMedia;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MyBusinessMediaController extends Controller
{
    /**
     * Store media for the user's business.
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'media_file' => ['required', 'file', 'mimes:jpeg,png,jpg,gif,mp4,mov,avi', 'max:10240'], // 10MB max
            'media_type' => ['required', 'in:image,video'],
            'caption' => ['nullable', 'string', 'max:255']
        ]);

        $userBusiness = $request->user()->business;

        if (!$userBusiness) {
            return response()->json([
                'message' => 'You do not own a business'
            ], 403);
        }

        $mediaPath = $request->file('media_file')->store('business-media', 'public');

        $media = BusinessMedia::create([
            'business_id' => $userBusiness->id,
            'media_url' => $mediaPath,
            'media_type' => $request->media_type,
            'caption' => $request->caption,
        ]);

        return response()->json([
            'data' => new BusinessMediaResource($media)
        ], 201);
    }
}
