<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\ReviewResource;
use App\Models\Review;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MyBusinessController extends Controller
{
    /**
     * Reply to a review for the user's business.
     */
    public function replyToReview(Request $request, Review $review): JsonResponse
    {
        $request->validate([
            'owner_reply' => ['required', 'string']
        ]);

        // Ensure the review belongs to the user's business
        $userBusiness = $request->user()->business;

        if (!$userBusiness || $review->business_id !== $userBusiness->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $review->update([
            'owner_reply' => $request->owner_reply
        ]);

        $review->load('user');

        return response()->json([
            'data' => new ReviewResource($review)
        ]);
    }
}
