<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreReviewRequest;
use App\Http\Resources\ReviewResource;
use App\Models\Business;
use App\Models\Review;
use Illuminate\Http\JsonResponse;

class ReviewController extends Controller
{
    /**
     * Store a new review for a business.
     */
    public function store(StoreReviewRequest $request, Business $business): JsonResponse
    {
        $review = Review::create([
            'user_id' => $request->user()->id,
            'business_id' => $business->id,
            'rating' => $request->rating,
            'comment' => $request->comment,
        ]);

        // Update business average rating and review count
        $this->updateBusinessRating($business);

        $review->load('user');

        return response()->json([
            'data' => new ReviewResource($review)
        ], 201);
    }

    /**
     * Update business average rating and review count.
     */
    private function updateBusinessRating(Business $business): void
    {
        $reviews = $business->reviews();

        $business->update([
            'average_rating' => $reviews->avg('rating'),
            'review_count' => $reviews->count(),
        ]);
    }
}
