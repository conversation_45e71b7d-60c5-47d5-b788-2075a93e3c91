# Business Owner Seeder - Complete Implementation

## Overview
A dedicated seeder has been created to provide a realistic business owner account for testing business owner functionality in the Local Directory API. This seeder creates a complete business with owner, reviews, media, and business hours.

## Business Owner Account Details

### Login Credentials
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Name**: <PERSON>
- **Role**: Business Owner

### Business Details
- **Business Name**: <PERSON>'s Authentic Pizzeria
- **Slug**: `marios-authentic-pizzeria`
- **Business ID**: 6
- **Description**: Family-owned authentic Italian pizzeria serving traditional Neapolitan pizza made with imported ingredients and wood-fired ovens. Established in 1985, we bring the taste of Italy to your neighborhood.
- **Phone**: ******-PIZZA-01
- **Website**: https://mariosauthenticpizza.com
- **Address**: 456 Little Italy Street, New York, NY 10012
- **Coordinates**: 40.7589, -73.9851 (Little Italy, NYC)
- **Categories**: Restaurants, Pizza
- **Status**: Active
- **Rating**: 4.6/5 (5 reviews)

### Business Hours
- **Monday**: Closed
- **Tuesday-Thursday**: 11:00 AM - 10:00 PM
- **Friday-Saturday**: 11:00 AM - 11:00 PM
- **Sunday**: 12:00 PM - 9:00 PM

### Business Media (4 items)
1. **Pizza Oven** (Image): "Our authentic wood-fired pizza oven imported from Naples"
2. **Margherita Pizza** (Image): "Classic Margherita pizza with fresh basil and mozzarella"
3. **Restaurant Interior** (Image): "Cozy Italian atmosphere with family photos and vintage decor"
4. **Chef Making Pizza** (Video): "Watch our master pizzaiolo craft the perfect pizza"

### Customer Reviews (5 reviews)

#### Review 1 - Sarah Johnson ⭐⭐⭐⭐⭐
- **Rating**: 5/5
- **Comment**: "Absolutely incredible! The pizza here is as authentic as it gets. The crust is perfectly crispy and the ingredients taste so fresh. Mario himself came to our table to check on us!"
- **Owner Reply**: "Grazie mille! It means the world to us that you enjoyed your experience. We look forward to welcoming you back soon! - Mario"

#### Review 2 - Mike Chen ⭐⭐⭐⭐⭐
- **Rating**: 5/5
- **Comment**: "Best pizza in the city! The wood-fired oven makes all the difference. The atmosphere is cozy and the staff is incredibly friendly. Will definitely be back!"
- **Owner Reply**: "Thank you so much for your kind words! Our wood-fired oven is indeed our pride and joy. See you again soon! - Mario"

#### Review 3 - Emily Rodriguez ⭐⭐⭐⭐
- **Rating**: 4/5
- **Comment**: "Great authentic Italian pizza. The Margherita was outstanding. Only minor complaint is that it can get quite busy on weekends, but it's worth the wait."
- **Owner Reply**: "Thank you for your patience and understanding! We're working on expanding to better serve our wonderful customers. Grazie! - Mario"

#### Review 4 - David Thompson ⭐⭐⭐⭐⭐
- **Rating**: 5/5
- **Comment**: "This place is a hidden gem! The owner is so passionate about his craft and it shows in every bite. The tiramisu is also to die for!"
- **Owner Reply**: *(Can be added by business owner)*

#### Review 5 - Lisa Anderson ⭐⭐⭐⭐
- **Rating**: 4/5
- **Comment**: "Excellent pizza and great service. The ingredients are clearly high quality and the prices are very reasonable for the quality you get."
- **Owner Reply**: *(Can be added by business owner)*

## Testing Business Owner Features

### 1. Login as Business Owner
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/login" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 2. View Business Details
```bash
curl -X GET "http://127.0.0.1:8000/api/v1/businesses/marios-authentic-pizzeria" \
  -H "Accept: application/json"
```

### 3. Reply to Reviews
```bash
# Reply to review ID 19 (David Thompson's review)
curl -X POST "http://127.0.0.1:8000/api/v1/my-business/reviews/19/reply" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -d '{"owner_reply":"Thank you so much! We are indeed passionate about bringing authentic Italian flavors to our community. The tiramisu is my nonna'\''s secret recipe! - Mario"}'
```

### 4. Upload Business Media
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/my-business/media" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -F "media_file=@/path/to/your/image.jpg" \
  -F "media_type=image" \
  -F "caption=New delicious pizza creation"
```

## Customer Accounts Created
The seeder also creates 5 customer accounts for testing:

1. **Sarah Johnson** - <EMAIL>
2. **Mike Chen** - <EMAIL>
3. **Emily Rodriguez** - <EMAIL>
4. **David Thompson** - <EMAIL>
5. **Lisa Anderson** - <EMAIL>

All customer accounts use password: `password123`

## Running the Seeder

### Fresh Migration with All Seeders
```bash
php artisan migrate:fresh --seed
```

### Run Only Business Owner Seeder
```bash
php artisan db:seed --class=BusinessOwnerSeeder
```

## Features Demonstrated

### ✅ Business Owner Authentication
- Secure login with Sanctum tokens
- Business ownership verification

### ✅ Review Management
- View all business reviews
- Reply to customer reviews
- Owner replies are displayed with reviews

### ✅ Media Management
- Upload business photos and videos
- Add captions to media
- Support for multiple media types

### ✅ Business Information
- Complete business profile
- Business hours management
- Category associations
- Geo-location data

### ✅ Customer Interaction
- Realistic customer reviews
- Varied review dates (1-30 days ago)
- Mix of ratings (4-5 stars)
- Authentic review content

## Integration with Flutter App

This business owner account provides everything needed to test:

1. **Business Owner Dashboard**
2. **Review Reply System**
3. **Media Upload Functionality**
4. **Business Profile Management**
5. **Customer Review Display**
6. **Business Hours Display**
7. **Category Management**

The seeded data is realistic and comprehensive, making it perfect for demonstrating all business owner features in the Flutter application.

## Notes

- The business has a high rating (4.6/5) to demonstrate a successful business
- Reviews span different dates to show timeline functionality
- Some reviews have owner replies, others don't (for testing reply functionality)
- Business hours include a closed day (Monday) to test closed day handling
- Media includes both images and video to test different media types
- All customer accounts can be used for testing customer features as well

This seeder provides a complete, realistic business owner scenario for comprehensive testing of the Local Directory API's business owner functionality.
