# Local Directory API - Complete Implementation Guide

## Overview
This document provides a comprehensive guide for implementing a Flutter mobile application that consumes the Local Directory API. The API is built with Laravel 12, uses Sanctum authentication, and provides full CRUD operations for a local business directory.

## API Base Information
- **Base URL**: `http://127.0.0.1:8000/api/v1`
- **Authentication**: Laravel Sanctum (Bearer Token)
- **Response Format**: JSON with `data` wrapper
- **Database**: SQLite (for development)

## Authentication Endpoints

### Register User
```http
POST /register
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123"
}
```

**Response (201):**
```json
{
  "data": {
    "user": {
      "id": 1,
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "created_at": "2025-07-20T10:49:35.000000Z"
    },
    "token": "1|H7VgkNaxDfo4R2ZdqHmN27Y2s0hX9opLWOIXtrt877973788"
  }
}
```

### Login User
```http
POST /login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200):**
```json
{
  "data": {
    "user": {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "created_at": "2025-07-20T10:49:35.000000Z"
    },
    "token": "1|H7VgkNaxDfo4R2ZdqHmN27Y2s0hX9opLWOIXtrt877973788"
  }
}
```

### Logout User
```http
POST /logout
Authorization: Bearer {token}
```

**Response (204):** No content

## Public Endpoints

### Get Categories
```http
GET /categories
```

**Response (200):**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Restaurants",
      "slug": "restaurants",
      "icon_url": "https://example.com/icons/restaurant.svg",
      "children": [
        {
          "id": 2,
          "name": "Fast Food",
          "slug": "fast-food",
          "icon_url": "https://example.com/icons/fastfood.svg"
        }
      ]
    }
  ]
}
```

### Get Businesses (with filtering and geo-search)
```http
GET /businesses?search=pizza&category=restaurants&rating=4&lat=40.7128&lng=-74.0060&page=1
```

**Query Parameters:**
- `search`: Search in business name/description
- `category`: Filter by category slug
- `rating`: Minimum rating (1-5)
- `lat`: Latitude for geo-search
- `lng`: Longitude for geo-search
- `page`: Pagination page number

**Response (200):**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Mario's Pizza Palace",
      "slug": "marios-pizza-palace",
      "logo_url": "https://example.com/logos/marios-pizza.jpg",
      "address": "123 Main St, Downtown",
      "latitude": "40.71280000",
      "longitude": "-74.00600000",
      "average_rating": 4.5,
      "review_count": 25,
      "distance_km": 0.5
    }
  ],
  "meta": {
    "current_page": 1,
    "last_page": 3,
    "per_page": 15,
    "total": 42
  }
}
```

### Get Single Business
```http
GET /businesses/{slug}
```

**Response (200):**
```json
{
  "data": {
    "id": 1,
    "name": "Mario's Pizza Palace",
    "slug": "marios-pizza-palace",
    "logo_url": "https://example.com/logos/marios-pizza.jpg",
    "address": "123 Main St, Downtown",
    "latitude": "40.71280000",
    "longitude": "-74.00600000",
    "average_rating": 4.5,
    "review_count": 25,
    "description": "Authentic Italian pizza made with fresh ingredients.",
    "phone_number": "******-0101",
    "website_url": "https://mariospizza.com",
    "categories": [
      {
        "id": 1,
        "name": "Restaurants",
        "slug": "restaurants",
        "icon_url": "https://example.com/icons/restaurant.svg"
      }
    ],
    "hours": [
      {
        "id": 1,
        "day_of_week": 1,
        "open_time": "09:00:00",
        "close_time": "17:00:00",
        "is_closed": false
      }
    ],
    "reviews": [
      {
        "id": 1,
        "rating": 5,
        "comment": "Amazing pizza!",
        "owner_reply": "Thank you for your review!",
        "created_at": "2025-07-20T10:48:54.000000Z",
        "user": {
          "id": 3,
          "name": "Jane Smith"
        }
      }
    ],
    "media": [
      {
        "id": 1,
        "media_url": "business-media/pizza-interior.jpg",
        "media_type": "image",
        "caption": "Our cozy interior"
      }
    ]
  }
}
```

## Authenticated Endpoints

### Get User Profile
```http
GET /me
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "data": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "created_at": "2025-07-20T10:49:35.000000Z"
  }
}
```

### Create Review
```http
POST /businesses/{business_id}/reviews
Authorization: Bearer {token}
Content-Type: application/json

{
  "rating": 5,
  "comment": "Excellent service and food quality!"
}
```

**Response (201):**
```json
{
  "data": {
    "id": 15,
    "rating": 5,
    "comment": "Excellent service and food quality!",
    "owner_reply": null,
    "created_at": "2025-07-20T11:30:00.000000Z",
    "user": {
      "id": 1,
      "name": "John Doe"
    }
  }
}
```

### Toggle Favorite
```http
POST /businesses/{business_id}/favorite
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "data": {
    "is_favorited": true,
    "message": "Business added to favorites"
  }
}
```

### Get User Favorites
```http
GET /me/favorites
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Mario's Pizza Palace",
      "slug": "marios-pizza-palace",
      "logo_url": "https://example.com/logos/marios-pizza.jpg",
      "address": "123 Main St, Downtown",
      "latitude": "40.71280000",
      "longitude": "-74.00600000",
      "average_rating": 4.5,
      "review_count": 25
    }
  ],
  "meta": {
    "current_page": 1,
    "last_page": 1,
    "per_page": 15,
    "total": 3
  }
}
```

## Business Owner Endpoints

### Reply to Review
```http
POST /my-business/reviews/{review_id}/reply
Authorization: Bearer {token}
Content-Type: application/json

{
  "owner_reply": "Thank you for your feedback! We appreciate your business."
}
```

**Response (200):**
```json
{
  "data": {
    "id": 15,
    "rating": 5,
    "comment": "Excellent service and food quality!",
    "owner_reply": "Thank you for your feedback! We appreciate your business.",
    "created_at": "2025-07-20T11:30:00.000000Z",
    "user": {
      "id": 1,
      "name": "John Doe"
    }
  }
}
```

### Upload Business Media
```http
POST /my-business/media
Authorization: Bearer {token}
Content-Type: multipart/form-data

media_file: [FILE]
media_type: "image" | "video"
caption: "Optional caption"
```

**Response (201):**
```json
{
  "data": {
    "id": 5,
    "media_url": "business-media/uploaded-image.jpg",
    "media_type": "image",
    "caption": "New menu item"
  }
}
```

## Error Responses

### Validation Error (422)
```json
{
  "message": "Validation failed",
  "errors": {
    "email": ["The email field is required."],
    "password": ["The password field is required."]
  }
}
```

### Unauthorized (401)
```json
{
  "message": "Invalid credentials"
}
```

### Forbidden (403)
```json
{
  "message": "Unauthorized"
}
```

### Not Found (404)
```json
{
  "message": "Resource not found"
}
```

## Data Models

### Business Hours
- `day_of_week`: 1-7 (Monday to Sunday)
- `open_time`: "HH:MM" format
- `close_time`: "HH:MM" format
- `is_closed`: boolean

### Review Rating
- Range: 1-5 (integer)
- Required field for reviews

### Media Types
- `image`: JPEG, PNG, GIF
- `video`: MP4, MOV, AVI
- Max file size: 10MB

## Seeded Test Data

The API comes with pre-seeded data including:

### Categories
- Restaurants (Fast Food, Fine Dining, Cafes, Pizza)
- Shopping (Clothing, Electronics, Grocery)
- Services (Auto Repair, Hair Salon, Cleaning)
- Health & Fitness (Gyms, Medical, Dental)
- Entertainment (Movies, Bars & Nightlife, Recreation)

### Sample Businesses
1. **Mario's Pizza Palace** - Pizza restaurant in Downtown
2. **TechHub Electronics** - Electronics store in Silicon Valley
3. **FitLife Gym** - Fitness center in Healthtown
4. **Bella's Hair Studio** - Hair salon in Styleville
5. **The Coffee Corner** - Cafe in Caffeine City

Each business includes:
- Complete business information
- Business hours (Mon-Fri 9-17, Sat 10-15, Sun closed)
- 3 sample reviews with ratings
- Category associations
- Realistic geo-coordinates

### Test User
- **Email**: <EMAIL>
- **Password**: password

## Implementation Notes for Flutter

### Authentication Flow
1. Store token securely (flutter_secure_storage)
2. Include token in Authorization header for protected routes
3. Handle token expiration and refresh

### Geo-location Features
1. Request location permissions
2. Get user's current location
3. Implement distance-based search
4. Show businesses on map with distance

### Key Features to Implement
1. **Business Listing** with search, filters, and pagination
2. **Business Details** with reviews, hours, media gallery
3. **User Authentication** with registration/login
4. **Review System** with rating submission
5. **Favorites Management**
6. **Geo-search** with map integration
7. **Business Owner Dashboard** for review replies and media upload

### Recommended Flutter Packages
- `http` or `dio` for API calls
- `flutter_secure_storage` for token storage
- `geolocator` for location services
- `google_maps_flutter` for map integration
- `image_picker` for media uploads
- `cached_network_image` for image caching
- `flutter_rating_bar` for rating displays

### Error Handling
- Implement proper error handling for all HTTP status codes
- Show user-friendly error messages
- Handle network connectivity issues
- Implement retry mechanisms for failed requests

This API is fully functional and ready for Flutter integration. All endpoints have been tested and are working correctly with the provided sample data.

## Quick Start for Testing

### 1. Start the Laravel Server
```bash
cd /path/to/local-directory-api
php artisan serve
```
Server will run at: `http://127.0.0.1:8000`

### 2. Test API Endpoints

**Get Categories:**
```bash
curl -X GET "http://127.0.0.1:8000/api/v1/categories" -H "Accept: application/json"
```

**Get Businesses:**
```bash
curl -X GET "http://127.0.0.1:8000/api/v1/businesses" -H "Accept: application/json"
```

**Register User:**
```bash
curl -X POST "http://127.0.0.1:8000/api/v1/register" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123","password_confirmation":"password123"}'
```

**Geo-search Businesses:**
```bash
curl -X GET "http://127.0.0.1:8000/api/v1/businesses?lat=40.7128&lng=-74.0060" -H "Accept: application/json"
```

### 3. Database Reset (if needed)
```bash
php artisan migrate:fresh --seed
```

## Flutter Implementation Checklist

### Core Features
- [ ] Authentication (Login/Register/Logout)
- [ ] Business Listing with Search & Filters
- [ ] Business Details View
- [ ] Review System (Create/View Reviews)
- [ ] Favorites Management
- [ ] User Profile Management
- [ ] Geo-location & Map Integration
- [ ] Business Owner Features (Review Replies, Media Upload)

### Technical Implementation
- [ ] HTTP Client Setup with Base URL
- [ ] Token-based Authentication
- [ ] Secure Token Storage
- [ ] Error Handling & User Feedback
- [ ] Pagination Implementation
- [ ] Image Caching & Display
- [ ] Location Services Integration
- [ ] Map Integration with Business Markers
- [ ] File Upload for Media
- [ ] Rating Widget Implementation

### UI/UX Considerations
- [ ] Responsive Design for Different Screen Sizes
- [ ] Loading States & Shimmer Effects
- [ ] Pull-to-Refresh Functionality
- [ ] Infinite Scroll for Business Listings
- [ ] Search with Debouncing
- [ ] Filter Bottom Sheet/Modal
- [ ] Business Card Components
- [ ] Review Components with Ratings
- [ ] Map View with Custom Markers
- [ ] Image Gallery for Business Media

### State Management
- [ ] Choose State Management Solution (Provider/Riverpod/Bloc)
- [ ] Authentication State Management
- [ ] Business Data Caching
- [ ] User Preferences Storage
- [ ] Location State Management

This comprehensive guide provides everything needed to build a complete Flutter application that integrates seamlessly with the Local Directory API.
