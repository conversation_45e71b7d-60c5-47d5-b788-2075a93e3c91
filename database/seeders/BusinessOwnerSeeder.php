<?php

namespace Database\Seeders;

use App\Models\Business;
use App\Models\BusinessHour;
use App\Models\BusinessMedia;
use App\Models\Category;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class BusinessOwnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a business owner user with known credentials
        $businessOwner = User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'email_verified_at' => now(),
        ]);

        // Create the business for this owner
        $business = Business::create([
            'user_id' => $businessOwner->id,
            'name' => '<PERSON>\'s Authentic Pizzeria',
            'description' => 'Family-owned authentic Italian pizzeria serving traditional Neapolitan pizza made with imported ingredients and wood-fired ovens. Established in 1985, we bring the taste of Italy to your neighborhood.',
            'phone_number' => '******-PIZZA-01',
            'website_url' => 'https://mariosauthenticpizza.com',
            'address' => '456 Little Italy Street, New York, NY 10012',
            'latitude' => 40.7589,
            'longitude' => -73.9851,
            'logo_url' => 'https://example.com/logos/marios-authentic.jpg',
            'is_active' => true,
            'average_rating' => 0,
            'review_count' => 0,
        ]);

        // Attach categories to the business
        $restaurantCategory = Category::where('name', 'Restaurants')->first();
        $pizzaCategory = Category::where('name', 'Pizza')->first();

        if ($restaurantCategory && $pizzaCategory) {
            $business->categories()->attach([$restaurantCategory->id, $pizzaCategory->id]);
        }

        // Create business hours (Tuesday-Sunday, closed Monday)
        $hours = [
            ['day' => 1, 'closed' => true],  // Monday - Closed
            ['day' => 2, 'open' => '11:00', 'close' => '22:00'], // Tuesday
            ['day' => 3, 'open' => '11:00', 'close' => '22:00'], // Wednesday
            ['day' => 4, 'open' => '11:00', 'close' => '22:00'], // Thursday
            ['day' => 5, 'open' => '11:00', 'close' => '23:00'], // Friday
            ['day' => 6, 'open' => '12:00', 'close' => '23:00'], // Saturday
            ['day' => 7, 'open' => '12:00', 'close' => '21:00'], // Sunday
        ];

        foreach ($hours as $hour) {
            BusinessHour::create([
                'business_id' => $business->id,
                'day_of_week' => $hour['day'],
                'open_time' => $hour['closed'] ?? false ? '00:00' : $hour['open'],
                'close_time' => $hour['closed'] ?? false ? '00:00' : $hour['close'],
                'is_closed' => $hour['closed'] ?? false,
            ]);
        }

        // Create some sample business media
        $mediaItems = [
            [
                'media_url' => 'business-media/pizza-oven.jpg',
                'media_type' => 'image',
                'caption' => 'Our authentic wood-fired pizza oven imported from Naples'
            ],
            [
                'media_url' => 'business-media/margherita-pizza.jpg',
                'media_type' => 'image',
                'caption' => 'Classic Margherita pizza with fresh basil and mozzarella'
            ],
            [
                'media_url' => 'business-media/restaurant-interior.jpg',
                'media_type' => 'image',
                'caption' => 'Cozy Italian atmosphere with family photos and vintage decor'
            ],
            [
                'media_url' => 'business-media/chef-making-pizza.mp4',
                'media_type' => 'video',
                'caption' => 'Watch our master pizzaiolo craft the perfect pizza'
            ],
        ];

        foreach ($mediaItems as $media) {
            BusinessMedia::create([
                'business_id' => $business->id,
                'media_url' => $media['media_url'],
                'media_type' => $media['media_type'],
                'caption' => $media['caption'],
            ]);
        }

        // Create some customer reviews for this business
        $customers = [
            ['name' => 'Sarah Johnson', 'email' => '<EMAIL>'],
            ['name' => 'Mike Chen', 'email' => '<EMAIL>'],
            ['name' => 'Emily Rodriguez', 'email' => '<EMAIL>'],
            ['name' => 'David Thompson', 'email' => '<EMAIL>'],
            ['name' => 'Lisa Anderson', 'email' => '<EMAIL>'],
        ];

        $reviews = [
            ['rating' => 5, 'comment' => 'Absolutely incredible! The pizza here is as authentic as it gets. The crust is perfectly crispy and the ingredients taste so fresh. Mario himself came to our table to check on us!'],
            ['rating' => 5, 'comment' => 'Best pizza in the city! The wood-fired oven makes all the difference. The atmosphere is cozy and the staff is incredibly friendly. Will definitely be back!'],
            ['rating' => 4, 'comment' => 'Great authentic Italian pizza. The Margherita was outstanding. Only minor complaint is that it can get quite busy on weekends, but it\'s worth the wait.'],
            ['rating' => 5, 'comment' => 'This place is a hidden gem! The owner is so passionate about his craft and it shows in every bite. The tiramisu is also to die for!'],
            ['rating' => 4, 'comment' => 'Excellent pizza and great service. The ingredients are clearly high quality and the prices are very reasonable for the quality you get.'],
        ];

        foreach ($customers as $index => $customerData) {
            // Create customer user
            $customer = User::create([
                'name' => $customerData['name'],
                'email' => $customerData['email'],
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]);

            // Create review
            $review = Review::create([
                'user_id' => $customer->id,
                'business_id' => $business->id,
                'rating' => $reviews[$index]['rating'],
                'comment' => $reviews[$index]['comment'],
                'created_at' => now()->subDays(rand(1, 30)),
            ]);

            // Add owner reply to some reviews
            if ($index < 3) {
                $ownerReplies = [
                    'Grazie mille! It means the world to us that you enjoyed your experience. We look forward to welcoming you back soon! - Mario',
                    'Thank you so much for your kind words! Our wood-fired oven is indeed our pride and joy. See you again soon! - Mario',
                    'Thank you for your patience and understanding! We\'re working on expanding to better serve our wonderful customers. Grazie! - Mario',
                ];

                $review->update(['owner_reply' => $ownerReplies[$index]]);
            }
        }

        // Update business rating and review count
        $avgRating = $business->reviews()->avg('rating');
        $reviewCount = $business->reviews()->count();

        $business->update([
            'average_rating' => round($avgRating, 2),
            'review_count' => $reviewCount,
        ]);

        $this->command->info('Business owner seeded successfully!');
        $this->command->info('Business Owner Credentials:');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
        $this->command->info('Business: Mario\'s Authentic Pizzeria');
    }
}
