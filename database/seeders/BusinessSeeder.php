<?php

namespace Database\Seeders;

use App\Models\Business;
use App\Models\BusinessHour;
use App\Models\Category;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BusinessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $businesses = [
            [
                'name' => '<PERSON>\'s Pizza Palace',
                'description' => 'Authentic Italian pizza made with fresh ingredients and traditional recipes.',
                'phone_number' => '******-0101',
                'website_url' => 'https://mariospizza.com',
                'address' => '123 Main St, Downtown',
                'latitude' => 40.7128,
                'longitude' => -74.0060,
                'logo_url' => 'https://example.com/logos/marios-pizza.jpg',
                'is_active' => true,
                'categories' => ['Pizza', 'Restaurants']
            ],
            [
                'name' => 'TechHub Electronics',
                'description' => 'Your one-stop shop for the latest electronics, gadgets, and tech accessories.',
                'phone_number' => '******-0102',
                'website_url' => 'https://techhub.com',
                'address' => '456 Tech Ave, Silicon Valley',
                'latitude' => 37.7749,
                'longitude' => -122.4194,
                'logo_url' => 'https://example.com/logos/techhub.jpg',
                'is_active' => true,
                'categories' => ['Electronics', 'Shopping']
            ],
            [
                'name' => 'FitLife Gym',
                'description' => 'State-of-the-art fitness facility with personal trainers and group classes.',
                'phone_number' => '******-0103',
                'website_url' => 'https://fitlifegym.com',
                'address' => '789 Fitness Blvd, Healthtown',
                'latitude' => 34.0522,
                'longitude' => -118.2437,
                'logo_url' => 'https://example.com/logos/fitlife.jpg',
                'is_active' => true,
                'categories' => ['Gyms', 'Health & Fitness']
            ],
            [
                'name' => 'Bella\'s Hair Studio',
                'description' => 'Professional hair styling, coloring, and beauty treatments in a relaxing environment.',
                'phone_number' => '******-0104',
                'website_url' => null,
                'address' => '321 Beauty Lane, Styleville',
                'latitude' => 41.8781,
                'longitude' => -87.6298,
                'logo_url' => 'https://example.com/logos/bellas.jpg',
                'is_active' => true,
                'categories' => ['Hair Salon', 'Services']
            ],
            [
                'name' => 'The Coffee Corner',
                'description' => 'Artisan coffee, fresh pastries, and a cozy atmosphere for work or relaxation.',
                'phone_number' => '******-0105',
                'website_url' => 'https://coffeecorner.com',
                'address' => '654 Brew Street, Caffeine City',
                'latitude' => 39.9526,
                'longitude' => -75.1652,
                'logo_url' => 'https://example.com/logos/coffee-corner.jpg',
                'is_active' => true,
                'categories' => ['Cafes', 'Restaurants']
            ]
        ];

        foreach ($businesses as $index => $businessData) {
            // Create or get a user for this business
            $user = User::factory()->create([
                'name' => 'Business Owner ' . ($index + 1),
                'email' => 'owner' . ($index + 1) . '@example.com',
            ]);

            $categories = $businessData['categories'];
            unset($businessData['categories']);

            $businessData['user_id'] = $user->id;
            $business = Business::create($businessData);

            // Attach categories
            $categoryIds = Category::whereIn('name', $categories)->pluck('id');
            $business->categories()->attach($categoryIds);

            // Create business hours (Mon-Fri 9-17, Sat 10-15, Sun closed)
            for ($day = 1; $day <= 7; $day++) {
                BusinessHour::create([
                    'business_id' => $business->id,
                    'day_of_week' => $day,
                    'open_time' => $day <= 5 ? '09:00' : ($day == 6 ? '10:00' : '09:00'),
                    'close_time' => $day <= 5 ? '17:00' : ($day == 6 ? '15:00' : '17:00'),
                    'is_closed' => $day == 7, // Sunday closed
                ]);
            }

            // Create some reviews
            $reviewers = User::factory(3)->create();
            foreach ($reviewers as $reviewer) {
                Review::create([
                    'user_id' => $reviewer->id,
                    'business_id' => $business->id,
                    'rating' => rand(3, 5),
                    'comment' => 'Great service and quality! Highly recommended.',
                ]);
            }

            // Update business rating
            $avgRating = $business->reviews()->avg('rating');
            $reviewCount = $business->reviews()->count();
            $business->update([
                'average_rating' => $avgRating,
                'review_count' => $reviewCount,
            ]);
        }
    }
}
