<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Restaurants',
                'icon_url' => 'https://example.com/icons/restaurant.svg',
                'children' => [
                    ['name' => 'Fast Food', 'icon_url' => 'https://example.com/icons/fastfood.svg'],
                    ['name' => 'Fine Dining', 'icon_url' => 'https://example.com/icons/finedining.svg'],
                    ['name' => 'Cafes', 'icon_url' => 'https://example.com/icons/cafe.svg'],
                    ['name' => 'Pizza', 'icon_url' => 'https://example.com/icons/pizza.svg'],
                ]
            ],
            [
                'name' => 'Shopping',
                'icon_url' => 'https://example.com/icons/shopping.svg',
                'children' => [
                    ['name' => 'Clothing', 'icon_url' => 'https://example.com/icons/clothing.svg'],
                    ['name' => 'Electronics', 'icon_url' => 'https://example.com/icons/electronics.svg'],
                    ['name' => 'Grocery', 'icon_url' => 'https://example.com/icons/grocery.svg'],
                ]
            ],
            [
                'name' => 'Services',
                'icon_url' => 'https://example.com/icons/services.svg',
                'children' => [
                    ['name' => 'Auto Repair', 'icon_url' => 'https://example.com/icons/autorepair.svg'],
                    ['name' => 'Hair Salon', 'icon_url' => 'https://example.com/icons/salon.svg'],
                    ['name' => 'Cleaning', 'icon_url' => 'https://example.com/icons/cleaning.svg'],
                ]
            ],
            [
                'name' => 'Health & Fitness',
                'icon_url' => 'https://example.com/icons/health.svg',
                'children' => [
                    ['name' => 'Gyms', 'icon_url' => 'https://example.com/icons/gym.svg'],
                    ['name' => 'Medical', 'icon_url' => 'https://example.com/icons/medical.svg'],
                    ['name' => 'Dental', 'icon_url' => 'https://example.com/icons/dental.svg'],
                ]
            ],
            [
                'name' => 'Entertainment',
                'icon_url' => 'https://example.com/icons/entertainment.svg',
                'children' => [
                    ['name' => 'Movies', 'icon_url' => 'https://example.com/icons/movies.svg'],
                    ['name' => 'Bars & Nightlife', 'icon_url' => 'https://example.com/icons/bars.svg'],
                    ['name' => 'Recreation', 'icon_url' => 'https://example.com/icons/recreation.svg'],
                ]
            ],
        ];

        foreach ($categories as $categoryData) {
            $children = $categoryData['children'] ?? [];
            unset($categoryData['children']);

            $category = Category::create($categoryData);

            foreach ($children as $childData) {
                $childData['parent_id'] = $category->id;
                Category::create($childData);
            }
        }
    }
}
