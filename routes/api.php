<?php

use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\BusinessController;
use App\Http\Controllers\Api\V1\CategoryController;
use App\Http\Controllers\Api\V1\FavoriteController;
use App\Http\Controllers\Api\V1\MyBusinessController;
use App\Http\Controllers\Api\V1\MyBusinessMediaController;
use App\Http\Controllers\Api\V1\ProfileController;
use App\Http\Controllers\Api\V1\ReviewController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::prefix('v1')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);

    // Public endpoints
    Route::get('/categories', [CategoryController::class, 'index']);
    Route::get('/businesses', [BusinessController::class, 'index']);
    Route::get('/businesses/{business:slug}', [BusinessController::class, 'show']);

    // Authenticated routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);

        // Profile
        Route::get('/me', [ProfileController::class, 'show']);

        // Reviews
        Route::post('/businesses/{business}/reviews', [ReviewController::class, 'store']);

        // Favorites
        Route::post('/businesses/{business}/favorite', [FavoriteController::class, 'toggle']);
        Route::get('/me/favorites', [FavoriteController::class, 'index']);

        // Business owner routes
        Route::post('/my-business/reviews/{review}/reply', [MyBusinessController::class, 'replyToReview']);

        // Business media upload
        Route::post('/my-business/media', [MyBusinessMediaController::class, 'store']);
    });
});
